<?php
/**
 * api/secure_reward.php
 * API эндпоинт для безопасного начисления награды за просмотр рекламы по токену.
 * ФИНАЛЬНАЯ ВЕРСИЯ, АДАПТИРОВАННАЯ ПОД КОНФИГ ПОЛЬЗОВАТЕЛЯ
 */

header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC
date_default_timezone_set('UTC');

// --- Подключение зависимостей ---
if (!(@require_once __DIR__ . '/config.php')) { http_response_code(500); error_log('FATAL: config.php not found in secure_reward.php'); echo json_encode(['error'=>'Ошибка сервера: CFG']); exit; }
if (!(@require_once __DIR__ . '/db_mock.php')) { http_response_code(500); error_log('FATAL: db_mock.php not found in secure_reward.php'); echo json_encode(['error'=>'Ошибка сервера: DBM']); exit; }
if (!(@require_once __DIR__ . '/functions.php')) { http_response_code(500); error_log('FATAL: functions.php not found in secure_reward.php'); echo json_encode(['error'=>'Ошибка сервера: FNC']); exit; }
if (!(@require_once __DIR__ . '/ad_limits_manager.php')) { http_response_code(500); error_log('FATAL: ad_limits_manager.php not found in secure_reward.php'); echo json_encode(['error'=>'Ошибка сервера: ALM']); exit; }
// --- Конец зависимостей ---

// --- Проверка доступности ключевых функций ---
if (!function_exists('loadUserData') || !function_exists('increaseUserBalance') || !function_exists('saveUserData')) {
    error_log('secure_reward FATAL: Одна из ключевых функций (loadUserData, increaseUserBalance, saveUserData) не определена. Проверьте functions.php.');
    http_response_code(500);
    echo json_encode(['error' => 'Критическая ошибка сервера: FNC_MISS']);
    exit;
}

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['token']) || empty($input['token'])) {
    error_log("secure_reward ERROR: Нет токена или неверный JSON.");
    http_response_code(400); echo json_encode(['error' => 'Ошибка запроса: Токен отсутствует']); exit;
}
$receivedToken = $input['token'];
error_log("secure_reward INFO: [ШАГ 1] Получен запрос на проверку токена: $receivedToken");


// 2. Загрузка и проверка токена
$tokenFile = __DIR__ . '/../database/ad_tokens.json';
if (!file_exists($tokenFile)) {
    error_log("secure_reward CRITICAL: Файл токенов не найден по пути: $tokenFile");
    http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: База токенов не найдена.']); exit;
}

$tokensData = file_get_contents($tokenFile);
$tokens = json_decode($tokensData, true);
if (!is_array($tokens)) {
    $tokens = [];
    error_log("secure_reward WARNING: Файл токенов был пуст или поврежден. Инициализирован пустой массив.");
}

$tokenInfo = null;
$tokenIndex = -1;

foreach ($tokens as $index => $t) {
    if (is_array($t) && isset($t['token']) && $t['token'] === $receivedToken) {
        $tokenInfo = $t;
        $tokenIndex = $index;
        break;
    }
}

if ($tokenInfo === null) {
    error_log("secure_reward WARNING: [ШАГ 2 - ОШИБКА] Токен '$receivedToken' не найден в базе. Содержимое базы: " . json_encode($tokens));
    http_response_code(404); echo json_encode(['error' => 'Токен не найден или недействителен']); exit;
}

error_log("secure_reward INFO: [ШАГ 2] Токен '$receivedToken' найден. Проверка статуса...");

if (!empty($tokenInfo['used'])) {
    error_log("secure_reward WARNING: Попытка повторного использования токена: $receivedToken");
    http_response_code(403); echo json_encode(['error' => 'Токен уже был использован']); exit;
}

if (time() > $tokenInfo['expires_at']) {
    error_log("secure_reward WARNING: Истек срок действия токена: $receivedToken");
    http_response_code(403); echo json_encode(['error' => 'Срок действия токена истек']); exit;
}

error_log("secure_reward INFO: [ШАГ 3] Токен валиден. Начинаем начисление награды...");

// 3. Токен валиден. Начисляем награду.
$userId = $tokenInfo['user_id'];
$adType = $tokenInfo['ad_type'];

$userData = loadUserData();
if (!isset($userData[$userId])) {
    error_log("secure_reward ERROR: Пользователь $userId из токена не найден в базе данных.");
    http_response_code(404); echo json_encode(['error' => 'Пользователь не найден']); exit;
}

// ======================= АДАПТИРОВАННЫЙ БЛОК ПОЛУЧЕНИЯ НАГРАДЫ =======================
$rewardAmount = 0; // Награда по умолчанию - 0. Если ни одно условие не сработает, ничего не начислится.
if ($adType === 'native_banner' && defined('AD_REWARD_NATIVE_BANNER')) {
    $rewardAmount = AD_REWARD_NATIVE_BANNER;
} elseif ($adType === 'interstitial' && defined('AD_REWARD_INTERSTITIAL')) {
    $rewardAmount = AD_REWARD_INTERSTITIAL;
} elseif ($adType === 'rewarded_video' && defined('AD_REWARD_REWARDED_VIDEO')) {
    $rewardAmount = AD_REWARD_REWARDED_VIDEO;
}
// ======================= КОНЕЦ АДАПТИРОВАННОГО БЛОКА =======================

if ($rewardAmount <= 0) {
    error_log("secure_reward WARNING: Для типа рекламы '$adType' не настроена награда (или она равна 0). Начисление пропущено.");
    // Награда не начисляется, но токен все равно нужно пометить как использованный, чтобы им не спамили.
} else {
    $newBalanceUser = increaseUserBalance($userId, $rewardAmount, $userData);
    if ($newBalanceUser === false) {
        error_log("secure_reward CRITICAL: Функция increaseUserBalance вернула false для пользователя $userId.");
        http_response_code(500); echo json_encode(['error' => 'Ошибка: Не удалось обновить баланс']); exit;
    }

    // Обновляем статистику
    $userData[$userId]['total_earned'] = ($userData[$userId]['total_earned'] ?? 0) + $rewardAmount;
    $userData[$userId]['ad_views_log'][] = ['time' => time(), 'reward' => $rewardAmount, 'type' => $adType];

    // КРИТИЧЕСКИ ВАЖНО: Обновляем лимиты в ad_limits.json
    try {
        $limitsManager = new AdLimitsManager();
        $newCount = $limitsManager->incrementUserAdCount($userId, $adType);
        $remaining = $limitsManager->getRemainingCount($userId, $adType);
        error_log("secure_reward INFO: [ЛИМИТЫ] Обновлены лимиты для пользователя $userId, тип $adType: $newCount просмотров, осталось $remaining");

        // === ЛОГИРОВАНИЕ УСПЕШНОГО ПРОСМОТРА ДЛЯ СТАТИСТИКИ ===
        $log_file = __DIR__ . '/../database/ad_views.json';
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) { mkdir($log_dir, 0775, true); }
        $log_entry = [
            'user_id' => $userId,
            'ad_type' => $adType,
            'reward' => $rewardAmount,
            'timestamp' => gmdate('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        $logs = [];
        if (file_exists($log_file)) {
            $logs = json_decode(file_get_contents($log_file), true);
            if (!is_array($logs)) { $logs = []; }
        }
        $logs[] = $log_entry;
        file_put_contents($log_file, json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        // === КОНЕЦ ЛОГИРОВАНИЯ ===

    } catch (Exception $e) {
        error_log("secure_reward ERROR: [ЛИМИТЫ] Ошибка обновления лимитов: " . $e->getMessage());
        // Не прерываем выполнение, так как награда уже начислена
    }

    // Начисляем реферальный бонус
    $referrerId = getUserReferrerId($userId, $userData);
    if ($referrerId !== null && isset($userData[$referrerId])) {
        $bonusPercent = defined('REFERRAL_BONUS_PERCENT') ? REFERRAL_BONUS_PERCENT : 0.10;
        $bonusAmount = floor($rewardAmount * $bonusPercent);
        if ($bonusAmount > 0) {
            increaseUserBalance($referrerId, $bonusAmount, $userData);
            $userData[$referrerId]['referral_earnings'] = ($userData[$referrerId]['referral_earnings'] ?? 0) + $bonusAmount;
        }
    }
    error_log("secure_reward INFO: [ШАГ 4] Награда $rewardAmount начислена. Текущий баланс в массиве: " . $userData[$userId]['balance']);
}


// 4. Помечаем токен как использованный и сохраняем
$tokens[$tokenIndex]['used'] = true;

if (file_put_contents($tokenFile, json_encode($tokens, JSON_PRETTY_PRINT), LOCK_EX) === false) {
    error_log("secure_reward CRITICAL: Не удалось обновить файл токенов после использования! ПРОВЕРЬТЕ ПРАВА ДОСТУПА К ФАЙЛУ: $tokenFile");
    // Не выходим, так как данные пользователя важнее
}

if (!saveUserData($userData)) {
    error_log("secure_reward CRITICAL: Не удалось сохранить данные пользователя после начисления! ПРОВЕРЬТЕ ПРАВА ДОСТУПА.");
    http_response_code(500); echo json_encode(['error' => 'Ошибка сервера: Не удалось сохранить данные']); exit;
}

error_log("secure_reward INFO: [ШАГ 5] Данные успешно сохранены. Отправляем ответ клиенту.");

// 5. Успешный ответ
http_response_code(200);
echo json_encode([
    'success' => true,
    'newBalance' => $userData[$userId]['balance'] ?? 0, // Берем финальный, уже сохраненный баланс
    'reward' => $rewardAmount,
    'message' => 'Награда успешно начислена'
]);
exit;
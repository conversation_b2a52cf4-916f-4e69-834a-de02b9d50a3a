<?php
header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC для всех операций с датой и временем
date_default_timezone_set('UTC');

// Подключаем аутентификацию
require_once __DIR__ . '/auth.php';
session_start();
if (!isAuthenticated()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// --- Функции ---

function getCountryCode($ip) {
    static $cache = [];
    if (isset($cache[$ip])) {
        return $cache[$ip];
    }

    if ($ip === '127.0.0.1' || $ip === 'unknown' || filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IS_PRIVATE | FILTER_FLAG_IS_RESERVED)) {
        return 'Unknown';
    }

    $url = "https://ip-api.com/json/{$ip}?fields=status,message,countryCode";
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        return 'Error';
    }
    $data = json_decode($response, true);
    if (!$data || $data['status'] !== 'success') {
        return 'Unknown';
    }
    $countryCode = $data['countryCode'] ?? 'Unknown';
    $cache[$ip] = $countryCode;
    return $countryCode;
}

function load_json_file($filepath) {
    if (!file_exists($filepath)) return [];
    $content = file_get_contents($filepath);
    if (empty($content)) return [];
    $data = json_decode($content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("JSON decode error in file: $filepath - " . json_last_error_msg());
        return [];
    }
    return is_array($data) ? $data : [];
}

// --- Основная логика ---

$clicks = load_json_file(__DIR__ . '/../../database/ad_clicks.json');
$views = load_json_file(__DIR__ . '/../../database/ad_views.json');

// Фильтрация
$date_from = !empty($_GET['date_from']) ? strtotime($_GET['date_from'] . ' 00:00:00') : null;
$date_to = !empty($_GET['date_to']) ? strtotime($_GET['date_to'] . ' 23:59:59') : null;
$ad_type_filter = $_GET['ad_type'] ?? 'all';

$filter_by_date = fn($entry) => (!$date_from || strtotime($entry['timestamp']) >= $date_from) && (!$date_to || strtotime($entry['timestamp']) <= $date_to);
$filter_by_ad_type = fn($entry) => $ad_type_filter === 'all' || ($entry['ad_type'] ?? 'unknown') === $ad_type_filter;

$filtered_clicks = array_filter($clicks, fn($c) => !empty($c['timestamp']) && $filter_by_date($c) && $filter_by_ad_type($c));
$filtered_views = array_filter($views, fn($v) => !empty($v['timestamp']) && $filter_by_date($v) && $filter_by_ad_type($v));

// Агрегация
$stats_by_type = [];
$stats_by_country = [];
$hourly_stats = array_fill(0, 24, ['clicks' => 0, 'views' => 0]);

$ad_types = ['native_banner', 'rewarded_video', 'interstitial'];
foreach ($ad_types as $type) {
    if ($ad_type_filter === 'all' || $ad_type_filter === $type) {
        $stats_by_type[$type] = ['clicks' => 0, 'views' => 0, 'rewards' => 0, 'ctr' => 0];
    }
}

foreach ($filtered_clicks as $click) {
    if (isset($click['click_type']) && $click['click_type'] === 'button_click') {
        $ad_type = $click['ad_type'] ?? 'unknown';
        if (isset($stats_by_type[$ad_type])) $stats_by_type[$ad_type]['clicks']++;

        $hour = (int)date('H', strtotime($click['timestamp']));
        $hourly_stats[$hour]['clicks']++;

        $country = getCountryCode($click['ip'] ?? 'unknown');
        $stats_by_country[$country] = ($stats_by_country[$country] ?? 0) + 1;
    }
}

foreach ($filtered_views as $view) {
    $ad_type = $view['ad_type'] ?? 'unknown';
    if (isset($stats_by_type[$ad_type])) {
        $stats_by_type[$ad_type]['views']++;
        $stats_by_type[$ad_type]['rewards'] += $view['reward'] ?? 0;
    }
    $hour = (int)date('H', strtotime($view['timestamp']));
    $hourly_stats[$hour]['views']++;
}

foreach ($stats_by_type as &$data) {
    if ($data['clicks'] > 0) $data['ctr'] = round(($data['views'] / $data['clicks']) * 100, 2);
}

arsort($stats_by_country);

$response = [
    'success' => true,
    'stats_by_type' => $stats_by_type,
    'stats_by_country' => $stats_by_country,
    'hourly_stats' => $hourly_stats,
    'last_updated' => date('Y-m-d H:i:s')
];

echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
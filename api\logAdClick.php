<?php
header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC
date_default_timezone_set('UTC');

// Простой логгер кликов по рекламе

$input = json_decode(file_get_contents('php://input'), true);

if (empty($input) || !isset($input['user_id']) || !isset($input['ad_type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid input']);
    exit;
}

$log_file = __DIR__ . '/../database/ad_clicks.json';
$log_dir = dirname($log_file);

// Создаем директорию, если она не существует
if (!is_dir($log_dir)) {
    mkdir($log_dir, 0775, true);
}

$log_entry = [
    'user_id' => $input['user_id'],
    'ad_type' => $input['ad_type'],
    'click_type' => $input['click_type'] ?? 'button_click',
    'reason' => $input['reason'] ?? '',
    'timestamp' => gmdate('Y-m-d H:i:s'),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
];

// Читаем существующий лог
$logs = [];
if (file_exists($log_file)) {
    $logs = json_decode(file_get_contents($log_file), true);
    if (!is_array($logs)) {
        $logs = [];
    }
}

// Добавляем новую запись
$logs[] = $log_entry;

// Сохраняем лог
file_put_contents($log_file, json_encode($logs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

echo json_encode(['success' => true]);

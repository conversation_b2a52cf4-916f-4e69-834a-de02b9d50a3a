<?php
header('Content-Type: application/json');

// Устанавливаем часовой пояс на UTC
date_default_timezone_set('UTC');

// Увеличиваем лимит памяти на всякий случай, если файлы логов большие
ini_set('memory_limit', '256M');

$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
$period = isset($_GET['period']) ? $_GET['period'] : 'last_7_days';
$dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
$dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;

if ($userId <= 0) {
    echo json_encode(['success' => false, 'error' => 'Invalid user ID']);
    exit;
}

function getUserActivityStats($userId, $period, $dateFrom, $dateTo) {
    $clicks_file = __DIR__ . '/../../database/ad_clicks.json';
    $views_file = __DIR__ . '/../../database/ad_views.json';

    $clicks = file_exists($clicks_file) ? json_decode(file_get_contents($clicks_file), true) : [];
    $views = file_exists($views_file) ? json_decode(file_get_contents($views_file), true) : [];

    if (!is_array($clicks)) $clicks = [];
    if (!is_array($views)) $views = [];

    // 1. Фильтруем записи только для нужного пользователя
    $user_clicks = array_filter($clicks, function($c) use ($userId) {
        return isset($c['user_id']) && $c['user_id'] == $userId && isset($c['click_type']) && $c['click_type'] === 'button_click';
    });

    $user_views = array_filter($views, function($v) use ($userId) {
        return isset($v['user_id']) && $v['user_id'] == $userId;
    });

    // 2. Определяем временные рамки
    $stats = [];
    $format = '';
    $interval = '';
    $startDate = null;
    $endDate = null;

    if ($period === 'last_24_hours') {
        $format = 'H:00';
        $interval = '-1 hour';
        $startDate = new DateTime('-23 hours');
        $endDate = new DateTime('now');
    } else { // Дневная статистика
        $format = 'Y-m-d';
        $interval = '+1 day';
        if ($period === 'last_7_days') {
            $startDate = new DateTime('-6 days');
            $endDate = new DateTime('today');
        } elseif ($period === 'custom_date' && !empty($dateFrom) && !empty($dateTo)) {
            $startDate = new DateTime($dateFrom);
            $endDate = new DateTime($dateTo);
        }
    }

    if (!$startDate) {
        return [];
    }

    // 3. Инициализируем массив статистики нулями
    $currentDate = clone $startDate;
    while ($currentDate <= $endDate) {
        $key = $currentDate->format($format);
        $stats[$key] = ['clicks' => 0, 'views' => 0];
        if ($period === 'last_24_hours') {
            $currentDate->modify('+1 hour');
        } else {
            $currentDate->modify($interval);
        }
    }

    // 4. Заполняем статистику кликами
    foreach ($user_clicks as $click) {
        $click_time = strtotime($click['timestamp'] . ' UTC');
        if ($click_time >= $startDate->getTimestamp() && $click_time <= $endDate->getTimestamp() + 86400) { // +1 день для включения всего дня
            $key = gmdate($format, $click_time);
            if (isset($stats[$key])) {
                $stats[$key]['clicks']++;
            }
        }
    }

    // 5. Заполняем статистику просмотрами
    foreach ($user_views as $view) {
        $view_time = strtotime($view['timestamp'] . ' UTC');
        if ($view_time >= $startDate->getTimestamp() && $view_time <= $endDate->getTimestamp() + 86400) {
            $key = gmdate($format, $view_time);
            if (isset($stats[$key])) {
                $stats[$key]['views']++;
            }
        }
    }
    
    if ($period === 'last_24_hours') {
        krsort($stats);
    }

    return $stats;
}

$data = getUserActivityStats($userId, $period, $dateFrom, $dateTo);

echo json_encode(['success' => true, 'data' => $data]);
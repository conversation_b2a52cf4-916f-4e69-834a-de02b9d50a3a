<?php
/**
 * api/cron/reset_daily_limits.php
 * Cron-скрипт для принудительного сброса лимитов рекламы в полночь UTC
 * Запускается каждый день в 00:01 UTC
 * 
 * Добавить в crontab:
 * 1 0 * * * /usr/bin/php /path/to/api/cron/reset_daily_limits.php
 */

// Включаем логирование
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../error.log');
error_reporting(E_ALL);

// Подключение зависимостей
try {
    require_once __DIR__ . '/../ad_limits_manager.php';
} catch (Exception $e) {
    error_log("ОШИБКА подключения ad_limits_manager.php: " . $e->getMessage());
    exit(1);
}

/**
 * Логирование действий сброса
 */
function logReset($message) {
    $timestamp = gmdate('Y-m-d H:i:s') . ' UTC';
    $logEntry = "[{$timestamp}] {$message}" . PHP_EOL;
    
    // Логируем в файл
    $logFile = __DIR__ . '/daily_reset.log';
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    
    // Также в error_log
    error_log("[DailyReset] {$message}");
}

try {
    logReset("=== Запуск принудительного сброса лимитов ===");
    
    // Получаем текущую дату UTC
    $currentUTCDate = gmdate('Y-m-d');
    $currentUTCTime = gmdate('H:i:s');
    
    logReset("Текущая дата UTC: {$currentUTCDate} {$currentUTCTime}");
    
    // Создаем экземпляр менеджера лимитов
    $limitsManager = new AdLimitsManager();
    
    // Получаем информацию о файле лимитов
    $limitsFile = __DIR__ . '/../database/ad_limits.json';
    
    if (!file_exists($limitsFile)) {
        logReset("❌ Файл лимитов не найден: {$limitsFile}");
        exit(1);
    }
    
    // Читаем текущие данные
    $jsonData = file_get_contents($limitsFile);
    $data = json_decode($jsonData, true);
    
    if (!$data) {
        logReset("❌ Ошибка чтения JSON данных из файла лимитов");
        exit(1);
    }
    
    $lastResetDate = $data['last_reset_date'] ?? 'неизвестно';
    $userCountsBefore = count($data['user_counts'] ?? []);
    
    logReset("Последний сброс: {$lastResetDate}");
    logReset("Пользователей с данными: {$userCountsBefore}");
    
    // Проверяем, нужен ли сброс
    if ($currentUTCDate === $lastResetDate) {
        logReset("✅ Сброс уже выполнен сегодня ({$currentUTCDate})");
        exit(0);
    }
    
    // Выполняем принудительный сброс
    $data['user_counts'] = [];
    $data['last_reset_date'] = $currentUTCDate;
    
    // Сохраняем обновленные данные
    $jsonDataNew = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
    if (file_put_contents($limitsFile, $jsonDataNew, LOCK_EX) === false) {
        logReset("❌ Ошибка записи обновленных данных в файл");
        exit(1);
    }
    
    logReset("✅ Принудительный сброс лимитов выполнен успешно");
    logReset("   - Предыдущая дата: {$lastResetDate}");
    logReset("   - Новая дата: {$currentUTCDate}");
    logReset("   - Сброшено данных пользователей: {$userCountsBefore}");
    
    // Дополнительная проверка - создаем новый экземпляр и проверяем
    $testManager = new AdLimitsManager();
    logReset("✅ Проверка: новый экземпляр AdLimitsManager создан успешно");
    
    logReset("=== Сброс лимитов завершен успешно ===");
    
} catch (Exception $e) {
    logReset("💥 КРИТИЧЕСКАЯ ОШИБКА: " . $e->getMessage());
    logReset("Стек вызовов: " . $e->getTraceAsString());
    exit(1);
}

?>

<?php
/**
 * Безопасный логгер для JSON файлов
 * Железобетонный алгоритм записи с защитой от race conditions и повреждения данных
 */

class SafeJsonLogger {
    private $maxRetries = 5;
    private $retryDelay = 100000; // 100ms в микросекундах
    private $lockTimeout = 10; // 10 секунд
    
    /**
     * Безопасно добавляет запись в JSON файл
     * 
     * @param string $filePath Путь к JSON файлу
     * @param array $newEntry Новая запись для добавления
     * @return bool true в случае успеха, false при ошибке
     */
    public function appendToJsonFile($filePath, $newEntry) {
        $attempts = 0;
        
        while ($attempts < $this->maxRetries) {
            $attempts++;
            
            try {
                // Создаем директорию если не существует
                $dir = dirname($filePath);
                if (!is_dir($dir)) {
                    if (!mkdir($dir, 0775, true)) {
                        error_log("SafeJsonLogger: Не удалось создать директорию: $dir");
                        return false;
                    }
                }
                
                // Получаем эксклюзивную блокировку файла
                $lockFile = $filePath . '.lock';
                $lockHandle = fopen($lockFile, 'c');
                
                if (!$lockHandle) {
                    error_log("SafeJsonLogger: Не удалось создать lock файл: $lockFile");
                    usleep($this->retryDelay);
                    continue;
                }
                
                // Пытаемся получить эксклюзивную блокировку
                $lockStart = time();
                while (!flock($lockHandle, LOCK_EX | LOCK_NB)) {
                    if (time() - $lockStart > $this->lockTimeout) {
                        fclose($lockHandle);
                        error_log("SafeJsonLogger: Таймаут блокировки файла: $filePath");
                        usleep($this->retryDelay);
                        continue 2; // Переходим к следующей попытке
                    }
                    usleep(10000); // 10ms
                }
                
                // Читаем существующие данные
                $existingData = $this->readJsonFileSafe($filePath);
                
                // Добавляем новую запись
                $existingData[] = $newEntry;
                
                // Записываем данные атомарно
                $success = $this->writeJsonFileAtomic($filePath, $existingData);
                
                // Освобождаем блокировку
                flock($lockHandle, LOCK_UN);
                fclose($lockHandle);
                
                // Удаляем lock файл
                if (file_exists($lockFile)) {
                    unlink($lockFile);
                }
                
                if ($success) {
                    return true;
                }
                
            } catch (Exception $e) {
                error_log("SafeJsonLogger: Ошибка при записи в $filePath: " . $e->getMessage());
            }
            
            // Ждем перед повторной попыткой
            usleep($this->retryDelay);
        }
        
        error_log("SafeJsonLogger: Не удалось записать в файл $filePath после $this->maxRetries попыток");
        return false;
    }
    
    /**
     * Безопасно читает JSON файл
     * 
     * @param string $filePath Путь к JSON файлу
     * @return array Массив данных или пустой массив при ошибке
     */
    private function readJsonFileSafe($filePath) {
        if (!file_exists($filePath)) {
            return [];
        }
        
        $content = file_get_contents($filePath);
        if ($content === false) {
            error_log("SafeJsonLogger: Не удалось прочитать файл: $filePath");
            return [];
        }
        
        if (empty($content)) {
            return [];
        }
        
        $data = json_decode($content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("SafeJsonLogger: Ошибка JSON в файле $filePath: " . json_last_error_msg());
            
            // Пытаемся восстановить из backup
            $backupFile = $filePath . '.backup';
            if (file_exists($backupFile)) {
                $backupContent = file_get_contents($backupFile);
                $backupData = json_decode($backupContent, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($backupData)) {
                    error_log("SafeJsonLogger: Восстановлено из backup: $backupFile");
                    return $backupData;
                }
            }
            
            return [];
        }
        
        return is_array($data) ? $data : [];
    }
    
    /**
     * Атомарно записывает данные в JSON файл
     * 
     * @param string $filePath Путь к JSON файлу
     * @param array $data Данные для записи
     * @return bool true в случае успеха
     */
    private function writeJsonFileAtomic($filePath, $data) {
        // Создаем backup существующего файла
        if (file_exists($filePath)) {
            $backupFile = $filePath . '.backup';
            if (!copy($filePath, $backupFile)) {
                error_log("SafeJsonLogger: Не удалось создать backup: $backupFile");
            }
        }
        
        // Кодируем данные в JSON
        $jsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        if ($jsonContent === false) {
            error_log("SafeJsonLogger: Ошибка кодирования JSON: " . json_last_error_msg());
            return false;
        }
        
        // Записываем во временный файл
        $tempFile = $filePath . '.tmp.' . uniqid();
        $bytesWritten = file_put_contents($tempFile, $jsonContent, LOCK_EX);
        
        if ($bytesWritten === false) {
            error_log("SafeJsonLogger: Не удалось записать во временный файл: $tempFile");
            return false;
        }
        
        // Проверяем целостность записанного файла
        $verifyContent = file_get_contents($tempFile);
        $verifyData = json_decode($verifyContent, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("SafeJsonLogger: Ошибка верификации JSON в $tempFile: " . json_last_error_msg());
            unlink($tempFile);
            return false;
        }
        
        // Атомарно перемещаем временный файл на место основного
        if (!rename($tempFile, $filePath)) {
            error_log("SafeJsonLogger: Не удалось переместить $tempFile в $filePath");
            unlink($tempFile);
            return false;
        }
        
        return true;
    }
    
    /**
     * Логирует клик по рекламе
     */
    public function logAdClick($userId, $adType, $clickType, $reason = '', $ip = '', $userAgent = '') {
        $logEntry = [
            'user_id' => $userId,
            'ad_type' => $adType,
            'click_type' => $clickType,
            'reason' => $reason,
            'timestamp' => gmdate('Y-m-d H:i:s'),
            'ip' => $ip ?: ($_SERVER['REMOTE_ADDR'] ?? 'unknown'),
            'user_agent' => $userAgent ?: ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown')
        ];
        
        $filePath = __DIR__ . '/../database/ad_clicks.json';
        return $this->appendToJsonFile($filePath, $logEntry);
    }
    
    /**
     * Логирует просмотр рекламы
     */
    public function logAdView($userId, $adType, $reward, $ip = '', $userAgent = '') {
        $logEntry = [
            'user_id' => $userId,
            'ad_type' => $adType,
            'reward' => $reward,
            'timestamp' => gmdate('Y-m-d H:i:s'),
            'ip' => $ip ?: ($_SERVER['REMOTE_ADDR'] ?? 'unknown'),
            'user_agent' => $userAgent ?: ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown')
        ];
        
        $filePath = __DIR__ . '/../database/ad_views.json';
        return $this->appendToJsonFile($filePath, $logEntry);
    }
}
?>
